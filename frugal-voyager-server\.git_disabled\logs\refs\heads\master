0000000000000000000000000000000000000000 d3ef68916a3b85153da05501db1ed29355302cf4 陈少郎 <<EMAIL>> 1746238378 +0800	commit (initial): init
d3ef68916a3b85153da05501db1ed29355302cf4 8047f8fc62c65b60d52cb382783ecd25a8ea56bb 陈少郎 <<EMAIL>> 1746338790 +0800	commit: feat: 增加接口要求
8047f8fc62c65b60d52cb382783ecd25a8ea56bb ba659cc06b9cc1b76a8badd9f0cfa42289c08e06 陈少郎 <<EMAIL>> 1746345115 +0800	commit: feat: 创建基础框架
ba659cc06b9cc1b76a8badd9f0cfa42289c08e06 23cb0c17a7bfa34d710439cff9e056447f1d686b 陈少郎 <<EMAIL>> 1746347000 +0800	commit: feat: 增肌简单的 langchain 例子
23cb0c17a7bfa34d710439cff9e056447f1d686b c084888d681e3d7a6d03ae2e44878f7724228c03 陈少郎 <<EMAIL>> 1746423718 +0800	commit: feat: 完成基础的景点搜索页面接口
c084888d681e3d7a6d03ae2e44878f7724228c03 d326f1e141ea9f337524577c9975d9ed090c5779 陈少郎 <<EMAIL>> 1746428258 +0800	commit: fix: 去除图片地址限制
d326f1e141ea9f337524577c9975d9ed090c5779 635221cbfad86822e8bf6191997a5af79cebdfe3 陈少郎 <<EMAIL>> 1746429928 +0800	commit: faet: 提取ai response 的结果字符串处理，单独提取成函数。
635221cbfad86822e8bf6191997a5af79cebdfe3 d9f518e111cd75dfe9f4fbb3e047d266a94cbf6f 陈少郎 <<EMAIL>> 1746430491 +0800	commit: feat: 增加12306接口
d9f518e111cd75dfe9f4fbb3e047d266a94cbf6f c36eae5400f6c11b457ce60c592866acb729041e 陈少郎 <<EMAIL>> 1746431338 +0800	commit: feat: 增加12306辅助数据
c36eae5400f6c11b457ce60c592866acb729041e 86212813c0ff2bc5ebed02398c0a4e2a994ee36e 陈少郎 <<EMAIL>> 1746604140 +0800	commit: feat: 完成12306接口调用
86212813c0ff2bc5ebed02398c0a4e2a994ee36e 032eb2439b6aba4abcb0b1663de11b7ccb3869c3 陈少郎 <<EMAIL>> 1747048398 +0800	commit: feat：添加攻略查询接口
032eb2439b6aba4abcb0b1663de11b7ccb3869c3 4334ebaebd35c1b78be76813f61ba037bacd18c1 陈少郎 <<EMAIL>> 1747140025 +0800	commit: feat: 增加测试用例
4334ebaebd35c1b78be76813f61ba037bacd18c1 54fbcea71aadc2ca1d09a7d592f4ff99a94d2743 陈少郎 <<EMAIL>> 1747140514 +0800	commit: update: 将说明改成中文
54fbcea71aadc2ca1d09a7d592f4ff99a94d2743 3c96f042754ecc28908fbefcf142a034026aba11 陈少郎 <<EMAIL>> 1747293293 +0800	commit: doc: 完善文档内容
3c96f042754ecc28908fbefcf142a034026aba11 0c55dc6e4cc0dd40003b9baadde85705292a9326 陈少郎 <<EMAIL>> 1747309044 +0800	commit: feat: 请求错误的时候增加报错信息，而不是返回默认的内容
0c55dc6e4cc0dd40003b9baadde85705292a9326 1541b9ec7769a103f4769acfa6ccc856147cfaa8 陈少郎 <<EMAIL>> 1747309320 +0800	commit: doc: 说明文档增加报错相关记录
1541b9ec7769a103f4769acfa6ccc856147cfaa8 6da396f596fca58d51a0171f6a0c59778d542ca6 陈少郎 <<EMAIL>> 1747315752 +0800	commit: feat: 增加多接口多模型功能
6da396f596fca58d51a0171f6a0c59778d542ca6 c667393b7971761322eb7d09520da9be2de670ad 陈少郎 <<EMAIL>> 1747385907 +0800	commit: update: 修改车票接口默认时间
c667393b7971761322eb7d09520da9be2de670ad d096859386689148f7366418d3ead7d351827599 陈少郎 <<EMAIL>> 1747398640 +0800	commit: feat: 火车票接口增加返程车票
d096859386689148f7366418d3ead7d351827599 1cc393423d4f54ede66495d3a63fa07fe4ef02d3 陈少郎 <<EMAIL>> 1747400299 +0800	commit: feat: 增加 12306 反爬机制
1cc393423d4f54ede66495d3a63fa07fe4ef02d3 355dfffa9bced68dacdbcad9c1044e70035c9e21 陈少郎 <<EMAIL>> 1747473636 +0800	commit: feat: 增加票价接口
355dfffa9bced68dacdbcad9c1044e70035c9e21 1cc393423d4f54ede66495d3a63fa07fe4ef02d3 陈少郎 <<EMAIL>> 1747536591 +0800	reset: moving to HEAD~
1cc393423d4f54ede66495d3a63fa07fe4ef02d3 d8e7011290167356fc7a6d43973e16938fd7fe94 陈少郎 <<EMAIL>> 1747552480 +0800	commit: feat: 车票接口增加车票价格获取方式
d8e7011290167356fc7a6d43973e16938fd7fe94 565baed9577e8673657245566f58bea374908186 陈少郎 <<EMAIL>> 1747635377 +0800	commit: fix: 修改车票查询接口默认总是以“杭州”为起点的问题
565baed9577e8673657245566f58bea374908186 fd44e00e3a5b4b49b7e300e8cc715448404f5616 陈少郎 <<EMAIL>> 1747641883 +0800	commit: feat: 计划安排页面增加车票属性，便于进行更详细的计划安排
fd44e00e3a5b4b49b7e300e8cc715448404f5616 7854ef89038588cdafbf7a7d69dbf20c4fe19bdd 陈少郎 <<EMAIL>> 1747646498 +0800	commit: update: 修改城市搜索提示词
7854ef89038588cdafbf7a7d69dbf20c4fe19bdd 3796dd18fe630b5f09c14401b1e946e9bde3e4db 陈少郎 <<EMAIL>> 1747794861 +0800	commit: feat: 增加设置，用于修改AI密钥
3796dd18fe630b5f09c14401b1e946e9bde3e4db 6536745e6d4e6843ab2130dff648d4ff6f2a1a86 陈少郎 <<EMAIL>> 1747807895 +0800	commit: feat: 后端设置必须上传OpenRouter 密钥
6536745e6d4e6843ab2130dff648d4ff6f2a1a86 dfab5ce61e921e4debb812ae1b23ca2be6d6d757 陈少郎 <<EMAIL>> 1747817148 +0800	commit: feat: 增加线上部署配置
dfab5ce61e921e4debb812ae1b23ca2be6d6d757 97acaa63c58dedb61b8f07fdcc4e4adb1d90fda2 陈少郎 <<EMAIL>> 1747823485 +0800	commit: fix: 修复计划安排mcp无法获取OpenRouter Key
97acaa63c58dedb61b8f07fdcc4e4adb1d90fda2 88ac0326294f8e82433ad6886c18934dea4b19a4 陈少郎 <<EMAIL>> 1747837779 +0800	commit: feat: 增加跨域配置
88ac0326294f8e82433ad6886c18934dea4b19a4 c5705c0fe2e3ae2748d81ea6cb475252f030b378 陈少郎 <<EMAIL>> 1747838164 +0800	commit: update: 开放所有的跨域权限
c5705c0fe2e3ae2748d81ea6cb475252f030b378 d33901511231efa6b8b87e80d545a5cf4230941d 陈少郎 <<EMAIL>> 1747838669 +0800	commit: update: 尝试修复跨域问题
d33901511231efa6b8b87e80d545a5cf4230941d 5577f35d69668c811de1f68b14a0524d6e39e084 陈少郎 <<EMAIL>> 1747839487 +0800	commit: update:允许所有跨域验证
5577f35d69668c811de1f68b14a0524d6e39e084 363660040ea7c0ecf9f9b112eb694ecc1adc2dc6 陈少郎 <<EMAIL>> 1747839981 +0800	commit: feat: 增加 tavily 密钥
