"""
OpenRouter中间件

用于从请求头中获取OpenRouter API Key和模型配置
"""
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from loguru import logger
from app.config import settings


class OpenRouterMiddleware(BaseHTTPMiddleware):
    """
    从请求头中获取OpenRouter API Key和模型配置的中间件
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        # 从请求头中获取OpenRouter API Key
        openrouter_api_key = request.headers.get("X-OpenRouter-API-Key")
        if openrouter_api_key:
            logger.debug("从请求头中获取到OpenRouter API Key")
            # 临时覆盖设置中的API Key
            settings.OPENROUTER_API_KEY = openrouter_api_key

        # 从请求头中获取OpenRouter模型配置
        default_model = request.headers.get("X-OpenRouter-Default-Model")
        city_model = request.headers.get("X-OpenRouter-City-Model")
        travel_guide_model = request.headers.get("X-OpenRouter-Travel-Guide-Model")

        # 临时覆盖设置中的模型配置
        if default_model:
            logger.debug(f"从请求头中获取到默认模型: {default_model}")
            settings.OPENROUTER_MODEL = default_model
            settings.OPENROUTER_MODELS["default"] = default_model

        if city_model:
            logger.debug(f"从请求头中获取到城市推荐模型: {city_model}")
            settings.OPENROUTER_CITY_MODEL = city_model
            settings.OPENROUTER_MODELS["city"] = city_model

        if travel_guide_model:
            logger.debug(f"从请求头中获取到旅游指南模型: {travel_guide_model}")
            settings.OPENROUTER_TRAVEL_GUIDE_MODEL = travel_guide_model
            settings.OPENROUTER_MODELS["travel_guide"] = travel_guide_model

        # 继续处理请求
        response = await call_next(request)
        return response
