# 极简版MCP客户端，直接支持城市名称作为参数
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent
import asyncio
import sys
import traceback
from loguru import logger
from app.config import settings
from app.utils.model_utils import get_openrouter_model


# 高德地图MCP服务器参数（windows）
server_params = StdioServerParameters(
    command="cmd",
    args=["/c", "npx", "-y", "@amap/amap-maps-mcp-server"],
    env={"AMAP_MAPS_API_KEY": settings.AMAP_MAPS_API_KEY},
)

# # 高德地图MCP服务器参数（Linux）
# server_params = StdioServerParameters(
#     command="npx",
#     args=["-y", "@amap/amap-maps-mcp-server"],
#     env={"AMAP_MAPS_API_KEY": settings.AMAP_MAPS_API_KEY},
# )


async def process_query(city, days=2, train_info=None, api_key=None, model_name=None):
    """
    处理用户查询，直接使用城市名称

    Args:
        city: 城市名称
        days: 旅游天数，默认为2天
        train_info: 火车票信息，包含出发和返程时间
        api_key: OpenRouter API Key，如果提供则使用此密钥
        model_name: 模型名称，如果提供则使用此模型
    """
    # 构建火车票信息字符串
    train_info_str = ""
    if train_info:
        train_info_str = f"""
注意：我已经预订了以下火车票，请根据火车时间安排行程：

去程火车：{train_info.departure_train}
出发时间：{train_info.departure_time}
到达时间：{train_info.arrival_time}

"""
        if train_info.return_train:
            train_info_str += f"""返程火车：{train_info.return_train}
返程出发时间：{train_info.return_departure_time}
返程到达时间：{train_info.return_arrival_time}
"""

    # 构建查询
    query = f"""帮我规划一下{city}附近{days}天的旅游行程，请严格按照以下格式输出结果，不要添加额外的解释。
{train_info_str}

请按照以下要求生成旅游行程：

1. 行程应该包含四种类型的项目：景点(attraction)、交通(transportation)、酒店(hotel)和餐厅(restaurant)
2. 每个景点项目应该包含以下字段：
   - id: 景点的唯一标识符，例如"city-attraction-1"
   - type: 固定为"attraction"
   - time: 游览时间段，例如"09:00 - 10:30"
   - name: 景点名称
   - description: 景点详细描述
   - ticketPrice: 门票价格，如"免费"或"¥50"
   - recommendedStay: 推荐游览时间，如"1.5小时"
   - imageUrl: 景点图片URL（可以使用任何合适的图片URL）
   - position: 显示位置，可以是"left"或"right"

3. 每个住宿项目应该包含以下字段（与景点格式相同）：
   - id: 住宿的唯一标识符，例如"city-hotel-1"、"city-hostel-1"或"city-homestay-1"
   - type: 固定为"attraction"
   - time: 入住/退房时间段，例如"14:00 - 次日12:00"
   - name: 住宿名称（可以是酒店、青旅、民宿等）
   - description: 住宿详细描述，包括位置、设施、特色等。对于民宿和青旅，可以描述其独特的氛围和体验
   - ticketPrice: 每晚价格，如"¥300/晚"、"¥80/床位"等
   - recommendedStay: 推荐入住时间，如"1晚"或"2晚"
   - imageUrl: 住宿图片URL
   - position: 显示位置，可以是"left"或"right"

4. 每个餐饮项目也应该包含以下字段（与景点格式相同）：
   - id: 餐饮的唯一标识符，例如"city-restaurant-1"、"city-snack-1"等
   - type: 固定为"attraction"
   - time: 用餐时间段，例如"08:00 - 08:30"（早餐）、"12:00 - 13:30"（午餐）、"18:00 - 19:30"（晚餐）
   - name: 餐饮场所名称（可以是餐厅、小吃店、街边摊等）
   - description: 详细描述，重点突出当地特色菜品、招牌菜、历史背景等。避免连锁餐厅，优先选择当地有特色的餐饮场所
   - ticketPrice: 人均消费，如"¥80/人"、"¥20/人"等
   - recommendedStay: 推荐用餐时间，如"30分钟"或"1.5小时"
   - imageUrl: 餐饮场所或特色菜品的图片URL
   - position: 显示位置，可以是"left"或"right"

5. 每个交通项目应该包含以下字段：
   - id: 交通的唯一标识符，例如"city-transport-1"
   - type: 固定为"transportation"
   - time: 交通时间段，例如"08:30 - 09:00"
   - icon: 交通方式的表情符号，根据实际交通方式选择：
     * 步行："🚶"
     * 出租车/网约车："🚕"
     * 公交车："🚌"
     * 地铁："🚇"
     * 火车："🚆"
     * 自行车/共享单车："🚲"
     * 摩托车/电动车："🛵"
   - duration: 交通所需时间，如"30分钟"、"15分钟"等，尽量准确
   - detail: 交通详情，必须明确指出起点和终点的具体名称，例如"从青年旅舍步行到老街早餐铺"、"从西湖景区打车到杭州酒家"

6. 行程应该按照时间顺序排列，包括：
   - 每天的住宿信息（第一天入住，最后一天退房）
   - 每天的三餐（早餐、午餐、晚餐）
   - 景点游览
   - 各点之间的交通
   - 特别注意：每天早上第一个活动（通常是早餐）前，必须安排从住宿地到早餐地点的交通
7. 行程应该覆盖{days}天的时间，每天从早上开始到晚上结束
8. 请确保行程合理，考虑交通时间、用餐时间和景点游览时间

输出格式应为JSON数组，每个元素是一个景点、酒店、餐厅或交通项目，例如：

```json
[
  {{
    "id": "{city}-homestay-1",
    "type": "attraction",
    "time": "14:00 - 次日12:00",
    "name": "老城区文艺民宿",
    "description": "位于{city}老城区的特色民宿，由百年老宅改造而成，保留了传统建筑风格，同时配备现代化设施。步行可达多个景点和美食街，是体验当地生活的绝佳选择。",
    "ticketPrice": "¥180/晚",
    "recommendedStay": "2晚",
    "imageUrl": "https://example.com/homestay.jpg",
    "position": "left"
  }},
  {{
    "id": "{city}-transport-morning-1",
    "type": "transportation",
    "time": "07:30 - 07:45",
    "icon": "🚶",
    "duration": "15分钟",
    "detail": "从老城区文艺民宿步行到老街早餐铺"
  }},
  {{
    "id": "{city}-breakfast-1",
    "type": "attraction",
    "time": "07:45 - 08:30",
    "name": "老街早餐铺",
    "description": "{city}当地传统早点，有百年历史，招牌菜包括豆浆油条、小笼包和葱油饼。这里的早餐是当地人日常生活的一部分，能够体验最地道的早晨氛围。",
    "ticketPrice": "¥15/人",
    "recommendedStay": "45分钟",
    "imageUrl": "https://example.com/breakfast.jpg",
    "position": "right"
  }},
  {{
    "id": "{city}-transport-2",
    "type": "transportation",
    "time": "08:30 - 09:00",
    "icon": "🚌",
    "duration": "30分钟",
    "detail": "从老街早餐铺乘坐5路公交车到古城墙景区"
  }},
  {{
    "id": "{city}-attraction-1",
    "type": "attraction",
    "time": "09:00 - 11:30",
    "name": "古城墙景区",
    "description": "保存完好的明代城墙，是{city}的标志性建筑之一。登上城墙可以俯瞰整个古城风貌，了解城市的历史变迁。城墙上设有多个角楼和城门，每个都有其独特的历史故事。",
    "ticketPrice": "¥60",
    "recommendedStay": "2.5小时",
    "imageUrl": "https://example.com/citywall.jpg",
    "position": "left"
  }},
  {{
    "id": "{city}-transport-3",
    "type": "transportation",
    "time": "11:30 - 11:45",
    "icon": "🚶",
    "duration": "15分钟",
    "detail": "从古城墙南门步行到回民街小吃区"
  }},
  {{
    "id": "{city}-lunch-1",
    "type": "attraction",
    "time": "11:45 - 13:15",
    "name": "回民街小吃",
    "description": "{city}最著名的美食聚集地，汇集了数十种当地特色小吃。推荐尝试羊肉泡馍、biangbiang面和肉夹馍等特色美食。这里的店铺多为家族传承数代，保留了最正宗的做法。",
    "ticketPrice": "¥40/人",
    "recommendedStay": "1.5小时",
    "imageUrl": "https://example.com/streetfood.jpg",
    "position": "right"
  }}
]
```

请确保输出是有效的JSON格式，并且包含足够详细的信息。特别注意：
1. 所有住宿（酒店、青旅、民宿）和餐饮（餐厅、小吃店、街边摊）的type字段都设置为"attraction"，以便与现有的前端展示兼容
2. 交通详情中必须明确指出起点和终点的具体名称
3. 每天都应包含三餐信息，优先选择当地特色餐饮
4. 第一天应有入住住宿信息，最后一天应有退房信息
5. 每天早上必须安排从住宿地到早餐地点的交通
6. 尽量选择多样化的交通方式，根据实际情况选择合适的交通工具
/no_think"""

    logger.info(f"正在查询: {city}的{days}天旅游行程...")
    print(f"正在查询: {city}的{days}天旅游行程...")

    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化连接
                await session.initialize()

                try:
                    # 加载MCP工具
                    logger.info("正在加载MCP工具...")
                    tools = await load_mcp_tools(session)
                    logger.info("MCP工具加载成功")

                    # 使用工具创建ReAct代理
                    logger.info("正在创建ReAct代理...")
                    # 使用旅游指南专用模型
                    model = get_openrouter_model(
                        service_name="travel_guide",
                        temperature=0.5,
                        api_key=api_key,
                        model_name=model_name,
                    )
                    agent = create_react_agent(model, tools)
                    logger.info("ReAct代理创建成功")

                    # 运行代理
                    logger.info("开始执行查询...")
                    agent_response = await agent.ainvoke(
                        {"messages": [{"role": "user", "content": query}]}
                    )
                    logger.info("查询执行完成")

                    # 从代理响应中提取内容
                    try:
                        if (
                            "messages" in agent_response
                            and len(agent_response["messages"]) > 0
                        ):
                            last_message = agent_response["messages"][-1]
                            if hasattr(last_message, "content"):
                                content = last_message.content
                                logger.info("成功提取响应内容")
                                print(content)
                                return
                            else:
                                logger.info("使用字典方式提取响应内容")
                                print(last_message)
                                return
                        else:
                            # 尝试其他可能的响应格式
                            logger.warning("响应格式不符合预期，尝试直接输出")
                            print(agent_response)
                            return
                    except Exception as e:
                        # 如果提取内容失败，直接打印整个响应
                        logger.error(f"提取响应内容时出错: {str(e)}")
                        logger.error(traceback.format_exc())
                        print(f"提取响应内容时出错: {str(e)}")
                        print(agent_response)
                        return

                except Exception as e:
                    error_msg = f"调用MCP服务时出错: {str(e)}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    print(error_msg)
                    return
    except Exception as e:
        error_msg = f"连接MCP服务时出错: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        print(error_msg)
        return


async def main():
    # 获取用户输入的城市名称
    if len(sys.argv) > 1:
        # 从命令行参数获取城市名称
        city = sys.argv[1]
        # 可选的天数参数
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    else:
        # 使用默认城市
        city = "杭州"
        days = 2

    await process_query(city, days)


if __name__ == "__main__":
    asyncio.run(main())
