import os
from typing import List
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()


class Settings:
    """
    应用程序设置。
    """

    # API配置
    API_V1_STR: str = "/api/v1"

    # 项目信息
    PROJECT_NAME: str = "Frugal Voyager API"
    PROJECT_DESCRIPTION: str = "Frugal Voyager旅行推荐应用程序的API"
    VERSION: str = "0.1.0"

    # CORS设置 - 硬编码，避免环境变量解析问题
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:5173",
        "http://************",
        "http://************:80",
        "http://************:5173",
        "http://localhost",
        "http://localhost:80",
        "http://localhost:8080",
        "http://127.0.0.1",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:80",
        "http://127.0.0.1:8080",
        # 添加通配符，允许所有来源（仅在开发环境中使用）
        "*",
    ]

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # 环境设置
    ENV: str = os.getenv("ENV", "development")

    # OpenRouter API配置 - 现在从前端设置获取，不再使用默认值
    OPENROUTER_API_KEY: str = os.getenv("OPENROUTER_API_KEY", "")

    # 默认模型 - 用于大多数API调用 - 现在从前端设置获取，不再使用默认值
    OPENROUTER_MODEL: str = os.getenv("OPENROUTER_MODEL", "")

    # 城市推荐模型 - 用于城市推荐API - 现在从前端设置获取，不再使用默认值
    OPENROUTER_CITY_MODEL: str = os.getenv("OPENROUTER_CITY_MODEL", "")

    # 旅游指南模型 - 用于旅游指南生成API - 现在从前端设置获取，不再使用默认值
    OPENROUTER_TRAVEL_GUIDE_MODEL: str = os.getenv("OPENROUTER_TRAVEL_GUIDE_MODEL", "")

    # 模型映射 - 用于根据服务名称获取对应的模型
    OPENROUTER_MODELS = {
        "default": OPENROUTER_MODEL,
        "city": OPENROUTER_CITY_MODEL,
        "travel_guide": OPENROUTER_TRAVEL_GUIDE_MODEL,
    }

    # 高德地图MCP API配置
    AMAP_MAPS_API_KEY: str = os.getenv(
        "AMAP_MAPS_API_KEY", "013b4beac378649e16ce2f009f76353e"
    )

    # Tavily API配置
    TAVILY_API_KEY: str = os.getenv("TAVILY_API_KEY", "")

    def __init__(self):
        # 从环境变量中读取配置（如果存在）
        if os.getenv("API_V1_STR"):
            self.API_V1_STR = os.getenv("API_V1_STR")

        if os.getenv("PROJECT_NAME"):
            self.PROJECT_NAME = os.getenv("PROJECT_NAME")

        if os.getenv("PROJECT_DESCRIPTION"):
            self.PROJECT_DESCRIPTION = os.getenv("PROJECT_DESCRIPTION")

        if os.getenv("VERSION"):
            self.VERSION = os.getenv("VERSION")

        # 从环境变量中读取CORS设置
        if os.getenv("BACKEND_CORS_ORIGINS"):
            origins = os.getenv("BACKEND_CORS_ORIGINS", "").split(",")
            # 过滤掉空字符串
            origins = [origin.strip() for origin in origins if origin.strip()]
            if origins:
                self.BACKEND_CORS_ORIGINS = origins


# 创建设置实例
settings = Settings()
