# 省钱旅行者 API

基于 FastAPI 的省钱旅行者旅游推荐应用后端。

## 功能特点

- 城市推荐 API
- 车票搜索 API
- 旅游指南生成 API
- 使用 Loguru 进行结构化日志记录
- 环境变量配置
- CORS 支持
- 全面的错误处理和统一的错误响应格式

## 项目结构

```plaintext
frugal-voyager-server/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── endpoints/
│   │       │   ├── cities.py          # 城市API端点
│   │       │   ├── tickets.py         # 车票API端点
│   │       │   └── travel_guides.py   # 旅游指南API端点
│   │       └── api.py                 # API路由配置
│   ├── schemas/
│   │   ├── city.py                    # 城市数据模型
│   │   ├── errors.py                  # 错误响应数据模型
│   │   ├── mcp.py                     # MCP服务数据模型
│   │   ├── ticket.py                  # 车票数据模型
│   │   └── travel_guide.py            # 旅游指南数据模型
│   ├── services/
│   │   ├── city_service.py            # 城市服务逻辑
│   │   ├── mcp_service.py             # MCP服务逻辑
│   │   ├── ticket_service.py          # 车票服务逻辑
│   │   └── travel_guide_service.py    # 旅游指南服务逻辑
│   ├── utils/
│   │   ├── ai_response_utils.py       # AI响应处理工具
│   │   ├── easy_mcp_client.py         # MCP客户端
│   │   ├── exceptions.py              # 自定义异常类
│   │   ├── json_utils.py              # JSON处理工具
│   │   ├── logging.py                 # 日志配置
│   │   ├── string_utils.py            # 字符串处理工具
│   │   └── train_api.py               # 列车API工具
│   ├── config.py                      # 应用配置
│   └── main.py                        # 主应用入口
├── logs/                              # 日志目录
├── tests/                             # 测试目录
│   ├── api/                           # API测试
│   │   └── v1/
│   │       ├── test_cities.py         # 城市API测试
│   │       ├── test_error_handling.py # 错误处理测试
│   │       ├── test_tickets.py        # 车票API测试
│   │       └── test_travel_guides.py  # 旅游指南API测试
│   ├── integration/                   # 集成测试
│   │   └── test_mcp_integration.py    # MCP集成测试
│   ├── utils/                         # 工具函数测试
│   │   ├── test_ai_response_utils.py  # AI响应处理工具测试
│   │   ├── test_json_utils.py         # JSON处理工具测试
│   │   └── test_string_utils.py       # 字符串处理工具测试
│   ├── conftest.py                    # 测试配置
│   └── README.md                      # 测试说明
├── .env                               # 环境变量
├── pyproject.toml                     # 项目配置
├── run.py                             # 运行脚本
├── frugal_voyager_server.egg-info/    # Python包元数据目录（安装-e模式时生成）
└── htmlcov/                           # 测试覆盖率HTML报告目录
```

## 环境要求

- Python 3.13+
- FastAPI
- Uvicorn
- Loguru
- Pydantic
- Python-dotenv

## 安装

1. 克隆仓库：

   ```bash
   git clone https://github.com/yourusername/frugal-voyager-server.git
   cd frugal-voyager-server
   ```

2. 创建虚拟环境：

   ```bash
   python -m venv .venv
   source .venv/bin/activate  # Windows系统: .venv\Scripts\activate
   ```

3. 安装依赖：

   ```bash
   pip install -e .
   ```

   或使用 uv（推荐）：

   ```bash
   uv pip install -e .
   ```

   > 注意：使用 `-e` 参数进行开发模式安装会在项目根目录生成 `frugal_voyager_server.egg-info` 目录，
   > 该目录包含包的元数据信息，用于 Python 包管理系统识别和管理项目依赖。

## 配置

配置通过环境变量管理，可以在`.env`文件中设置：

```env
# 环境设置
ENV=development

# API设置
API_V1_STR=/api/v1

# 项目信息
PROJECT_NAME=省钱旅行者 API
PROJECT_DESCRIPTION=省钱旅行者旅游推荐应用的API
VERSION=0.1.0

# CORS设置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:5173

# 日志
LOG_LEVEL=INFO

# OpenRouter API配置 - 现在从前端设置获取
OPENROUTER_API_KEY=
# 默认模型 - 用于大多数API调用 - 现在从前端设置获取
OPENROUTER_MODEL=
# 城市推荐模型 - 用于城市推荐API - 现在从前端设置获取
OPENROUTER_CITY_MODEL=
# 旅游指南模型 - 用于旅游指南生成API - 现在从前端设置获取
OPENROUTER_TRAVEL_GUIDE_MODEL=

# MCP服务器配置
# 操作系统类型：windows 或 linux
MCP_OS_TYPE=windows

# Windows环境下的MCP服务器配置
MCP_WINDOWS_COMMAND=cmd
MCP_WINDOWS_ARGS=/c,npx,-y,@amap/amap-maps-mcp-server

# Linux环境下的MCP服务器配置
MCP_LINUX_COMMAND=npx
MCP_LINUX_ARGS=-y,@amap/amap-maps-mcp-server

# Tavily API配置
TAVILY_API_KEY=your_tavily_api_key_here
```

### 多模型配置

本项目支持为不同的 API 服务使用不同的 AI 模型。您可以在前端设置页面中配置以下模型：

1. **默认模型** (`OPENROUTER_MODEL`): 用于大多数 API 调用，默认为 `google/gemini-2.5-flash-preview`
2. **城市推荐模型** (`OPENROUTER_CITY_MODEL`): 用于城市推荐 API，默认为 `anthropic/claude-3-opus:beta`
3. **旅游指南模型** (`OPENROUTER_TRAVEL_GUIDE_MODEL`): 用于旅游指南生成 API，默认为 `anthropic/claude-3-sonnet:beta`

您可以根据需要更改这些模型，以适应不同 API 的特定需求。例如，城市推荐 API 可能需要更强大的模型来生成详细的城市描述，而旅游指南 API 可能需要更擅长结构化输出的模型。

> **注意**：OpenRouter API Key 和模型配置现在从前端设置页面获取，不再从环境变量中读取。前端会在每次请求中通过请求头将这些配置传递给后端。

### MCP 服务器配置

本项目使用 MCP（Model Context Protocol）服务器来集成高德地图 API。配置支持不同的操作系统环境：

1. **操作系统类型** (`MCP_OS_TYPE`): 设置为 `windows` 或 `linux`，系统会根据此设置选择相应的命令配置
2. **Windows 配置**:
   - `MCP_WINDOWS_COMMAND`: Windows 下的命令，默认为 `cmd`
   - `MCP_WINDOWS_ARGS`: Windows 下的参数，默认为 `/c,npx,-y,@amap/amap-maps-mcp-server`
3. **Linux 配置**:
   - `MCP_LINUX_COMMAND`: Linux 下的命令，默认为 `npx`
   - `MCP_LINUX_ARGS`: Linux 下的参数，默认为 `-y,@amap/amap-maps-mcp-server`

系统会根据 `MCP_OS_TYPE` 的设置自动选择合适的配置。在生产环境中，通常设置为 `linux`；在开发环境中，根据您的操作系统进行设置。

## 运行应用

```bash
python run.py
```

API 将在 [http://localhost:8000](http://localhost:8000) 上可用。

API 文档可在以下位置查看：

- Swagger UI: [http://localhost:8000/docs](http://localhost:8000/docs)
- ReDoc: [http://localhost:8000/redoc](http://localhost:8000/redoc)

## 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/api/v1/test_cities.py

# 运行特定测试函数
pytest tests/api/v1/test_cities.py::test_ai_recommend_cities

# 运行特定测试目录
pytest tests/api/
```

### 测试覆盖率

```bash
# 生成测试覆盖率报告
pytest --cov=app

# 生成HTML格式的覆盖率报告
pytest --cov=app --cov-report=html

# 生成HTML报告后，可以在htmlcov目录中查看详细的覆盖率报告
# 打开htmlcov/index.html查看覆盖率概览
```

详细的测试说明请参阅 [tests/README.md](tests/README.md)。

## API 接口

### 错误处理

所有 API 接口在发生错误时都会返回统一格式的错误响应：

```json
{
  "status_code": 400,
  "message": "请求参数无效",
  "details": [
    {
      "loc": ["body", "city"],
      "msg": "城市名称不能为空",
      "type": "value_error.missing"
    }
  ],
  "error_code": "E400"
}
```

错误响应字段说明：

| 字段        | 类型             | 说明                                 |
| ----------- | ---------------- | ------------------------------------ |
| status_code | 整数             | HTTP 状态码                          |
| message     | 字符串           | 错误消息                             |
| details     | 数组/对象/字符串 | 错误详情，格式根据错误类型不同而变化 |
| error_code  | 字符串           | 错误代码，用于标识错误类型           |

常见错误代码：

| 错误代码 | 说明           |
| -------- | -------------- |
| E400     | 无效请求       |
| E404     | 资源未找到     |
| E500     | 服务器内部错误 |
| E4001    | 城市未找到     |
| E5001    | AI 服务错误    |
| E5002    | MCP 服务错误   |
| E5003    | 票务服务错误   |
| E5004    | 外部 API 错误  |

> 注意：在生产环境中，错误详情会被简化，以提高安全性。

### 城市推荐

```http
POST /api/v1/cities/recommend
```

请求体：

```json
{
  "position": "杭州"
}
```

响应：

```json
[
  {
    "id": "tokyo",
    "name": "日本东京",
    "description": "东京是一座充满活力的城市，融合了传统文化与现代科技。这里有美食、购物、历史景点和先进科技，适合各类旅行者。",
    "rating": "4.8",
    "cost": "中等",
    "recommendedStay": "5-7 天",
    "imageUrl": "https://images.unsplash.com/photo-1503899036084-c55cdd92da26?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
  }
]
```

### 车票搜索

```http
POST /api/v1/tickets/search
```

请求体：

```json
{
  "city_name": "上海",
  "departure_date": "2023-07-15",
  "return_date": "2023-07-20" // 可选，如果提供则会返回返程车票
}
```

> **接口变更说明**：
>
> 1. 此接口已更新，移除了原有的 `availableDates` 字段，现在返回 `departureTickets`（去程车票）和 `returnTickets`（返程车票）两个字段。当提供 `return_date` 参数时，会同时返回去程和返程的车票信息。
> 2. `tags` 字段已从字符串数组更改为对象数组，每个对象包含 `name`（座位名称）、`count`（票数）和 `price`（票价）三个字段。

响应：

```json
{
  "departureTickets": [
    {
      "id": "G105",
      "departureTime": "08:30",
      "departureStation": "杭州东",
      "arrivalTime": "10:30",
      "arrivalStation": "上海虹桥",
      "duration": "2小时",
      "price": "¥100",
      "trainNumber": "G105",
      "tags": [
        {
          "name": "二等座",
          "count": 10,
          "price": 100
        },
        {
          "name": "一等座",
          "count": 5,
          "price": 180
        }
      ]
    }
  ],
  "returnTickets": [
    {
      "id": "G106",
      "departureTime": "16:30",
      "departureStation": "上海虹桥",
      "arrivalTime": "18:30",
      "arrivalStation": "杭州东",
      "duration": "2小时",
      "price": "¥100",
      "trainNumber": "G106",
      "tags": [
        {
          "name": "二等座",
          "count": 8,
          "price": 100
        },
        {
          "name": "一等座",
          "count": 3,
          "price": 180
        }
      ]
    }
  ]
}
```

### 旅游指南生成

```http
POST /api/v1/travel-guides/generate-guide
```

请求体：

```json
{
  "city": "上海",
  "days": 2
}
```

响应：

```json
[
  {
    "id": "transport-initial",
    "type": "transportation",
    "time": "07:00 - 08:00",
    "icon": "✈️",
    "duration": "1小时",
    "detail": "从北京首都机场飞往上海虹桥机场"
  },
  {
    "id": "shanghai-hotel",
    "type": "attraction",
    "time": "09:00 - 10:30",
    "name": "上海迎宾馆",
    "description": "上海迎宾馆是上海地标性建筑，融合了中西建筑风格，可以欣赏到独特的建筑艺术。",
    "ticketPrice": "免费",
    "recommendedStay": "1.5小时",
    "imageUrl": "https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    "position": "left"
  }
  // 更多项目...
]
```

## 许可证

MIT
